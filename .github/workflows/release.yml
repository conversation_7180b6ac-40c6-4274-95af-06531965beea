name: Release Go Project

on:
  push:
    tags:
      - 'v*.*.*' # Trigger on version tags like v1.0.0, v1.2.3

permissions:
  contents: write # Needed to create releases and upload assets

jobs:
  goreleaser:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          # Fetch all history so goreleaser can generate a changelog
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.22' # Specify your Go version, or use 'stable'

      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@v6
        with:
          # Instruct goreleaser to run the release process
          args: release --clean
        env:
          # GitHub token is required to create releases and upload assets
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
